# Deployment Guide

This guide will help you deploy the Church Scheduler application to Vercel with a PostgreSQL database.

## Prerequisites

- A Vercel account
- Git repository (GitHub, GitLab, or Bitbucket)
- Node.js 18+ installed locally

## Database Setup

### 1. Create Vercel Postgres Database

1. Go to your [Vercel Dashboard](https://vercel.com/dashboard)
2. Navigate to the "Storage" tab
3. Click "Create Database"
4. Select "Postgres"
5. Choose a database name (e.g., `church-scheduler-db`)
6. Select a region close to your users
7. Click "Create"

### 2. Connect Database to Project

1. In your Vercel dashboard, go to your project settings
2. Navigate to "Environment Variables"
3. The Postgres database will automatically provide these variables:
   - `POSTGRES_URL`
   - `POSTGRES_PRISMA_URL`
   - `POSTGRES_URL_NO_SSL`
   - `POSTGRES_URL_NON_POOLING`
   - `POSTGRES_USER`
   - `POSTGRES_HOST`
   - `POSTGRES_PASSWORD`
   - `POSTGRES_DATABASE`

## Deployment Steps

### 1. Prepare Your Repository

Ensure your code is pushed to a Git repository (GitHub, GitLab, or Bitbucket).

### 2. Connect to Vercel

1. Go to [Vercel Dashboard](https://vercel.com/dashboard)
2. Click "New Project"
3. Import your Git repository
4. Configure the project:
   - **Framework Preset**: Next.js
   - **Root Directory**: `./` (if your Next.js app is in the root)
   - **Build Command**: `yarn build` (this will run migrations automatically)
   - **Install Command**: `yarn install`

### 3. Environment Variables

The database environment variables will be automatically set when you connect the Postgres database to your project.

### 4. Deploy

1. Click "Deploy"
2. Vercel will:
   - Install dependencies
   - Run database migrations (`yarn db:migrate`)
   - Seed the database with initial data (`yarn db:seed`)
   - Build the Next.js application
   - Deploy to production

## Automatic Migrations

The application is configured to run migrations automatically on every build:

```json
{
  "scripts": {
    "build": "yarn db:migrate && yarn db:seed && next build"
  }
}
```

This ensures that:
- Database schema is always up to date
- Initial data is seeded if the database is empty
- The application builds successfully

## Database Management

### Local Development

For local development, you can either:

1. **Use Vercel Postgres** (recommended):
   - Install Vercel CLI: `npm i -g vercel`
   - Link your project: `vercel link`
   - Pull environment variables: `vercel env pull .env.local`

2. **Use Local PostgreSQL**:
   - Install PostgreSQL locally
   - Create a database: `createdb church_scheduler`
   - Update `.env.local` with your local database URL

### Database Commands

```bash
# Generate new migration after schema changes
yarn db:generate

# Run migrations
yarn db:migrate

# Seed database with initial data
yarn db:seed

# Open Drizzle Studio (database GUI)
yarn db:studio

# Push schema changes directly (development only)
yarn db:push
```

## Monitoring and Maintenance

### Database Monitoring

1. Use Vercel's database dashboard to monitor:
   - Connection count
   - Query performance
   - Storage usage

2. Set up alerts for:
   - High connection usage
   - Slow queries
   - Storage limits

### Backup Strategy

Vercel Postgres automatically handles:
- Daily backups
- Point-in-time recovery
- High availability

## Troubleshooting

### Common Issues

1. **Migration Failures**:
   - Check Vercel build logs
   - Ensure database connection is working
   - Verify environment variables are set

2. **Database Connection Issues**:
   - Check if database is properly connected to project
   - Verify environment variables in Vercel dashboard
   - Ensure database is in the same region as your deployment

3. **Build Failures**:
   - Check that all dependencies are in `package.json`
   - Verify TypeScript types are correct
   - Ensure database schema is valid

### Getting Help

- Check Vercel documentation: https://vercel.com/docs
- Drizzle ORM documentation: https://orm.drizzle.team/
- Open an issue in the project repository

## Security Considerations

1. **Environment Variables**: Never commit `.env` files to version control
2. **Database Access**: Use connection pooling (automatically handled by Vercel Postgres)
3. **API Security**: Implement proper authentication and authorization
4. **CORS**: Configure CORS settings for production domains

## Performance Optimization

1. **Database Queries**: Use indexes for frequently queried columns
2. **Connection Pooling**: Enabled by default with Vercel Postgres
3. **Caching**: Implement caching strategies for frequently accessed data
4. **CDN**: Vercel automatically provides CDN for static assets
