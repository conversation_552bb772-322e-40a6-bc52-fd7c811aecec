#!/bin/bash

# Church Scheduler Setup Script
echo "🏛️  Setting up Church Scheduler..."

# Check if Node.js is installed
if ! command -v node &> /dev/null; then
    echo "❌ Node.js is not installed. Please install Node.js 18+ first."
    exit 1
fi

# Check if Yarn is installed
if ! command -v yarn &> /dev/null; then
    echo "📦 Installing Yarn..."
    npm install -g yarn
fi

# Install dependencies
echo "📦 Installing dependencies..."
yarn install

# Copy environment file if it doesn't exist
if [ ! -f .env.local ]; then
    echo "📝 Creating environment file..."
    cp .env.example .env.local
    echo "⚠️  Please edit .env.local with your database configuration"
fi

# Check if database URL is set
if grep -q "postgres://username:password" .env.local; then
    echo "⚠️  Warning: Please update .env.local with your actual database credentials"
    echo "   You can use Vercel Postgres or a local PostgreSQL instance"
fi

echo "✅ Setup complete!"
echo ""
echo "Next steps:"
echo "1. Update .env.local with your database configuration"
echo "2. Run 'yarn db:generate' to create migration files"
echo "3. Run 'yarn db:migrate' to set up the database"
echo "4. Run 'yarn db:seed' to add initial data"
echo "5. Run 'yarn dev' to start the development server"
echo ""
echo "For deployment to Vercel, see DEPLOYMENT.md"
