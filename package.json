{"name": "church-scheduler", "version": "0.0.1", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "yarn db:migrate && yarn db:seed && next build", "start": "next start", "lint": "eslint . --ext .ts,.tsx -c .eslintrc.json --fix", "db:generate": "drizzle-kit generate", "db:migrate": "tsx lib/db/migrate.ts", "db:seed": "tsx lib/db/seed.ts", "db:studio": "drizzle-kit studio", "db:push": "drizzle-kit push"}, "dependencies": {"@fullcalendar/core": "^6.1.17", "@fullcalendar/daygrid": "^6.1.17", "@fullcalendar/interaction": "^6.1.17", "@fullcalendar/list": "^6.1.17", "@fullcalendar/react": "^6.1.17", "@fullcalendar/timegrid": "^6.1.17", "@heroui/react": "^2.7.6", "@internationalized/date": "^3.8.2", "@react-aria/ssr": "3.9.7", "@react-aria/visually-hidden": "3.8.21", "@vercel/postgres": "^0.10.0", "clsx": "2.1.1", "date-fns": "^4.1.0", "drizzle-orm": "^0.44.2", "framer-motion": "11.13.1", "intl-messageformat": "^10.5.0", "next": "15.0.4", "next-themes": "^0.4.4", "react": "18.3.1", "react-big-calendar": "1.18.0", "react-dom": "18.3.1", "swr": "^2.3.3"}, "devDependencies": {"@next/eslint-plugin-next": "15.0.4", "@react-types/shared": "3.25.0", "@types/node": "20.5.7", "@types/react": "18.3.3", "@types/react-big-calendar": "^1.16.1", "@types/react-dom": "18.3.0", "@typescript-eslint/eslint-plugin": "8.11.0", "@typescript-eslint/parser": "8.11.0", "autoprefixer": "10.4.19", "drizzle-kit": "^0.31.2", "eslint": "^8.57.0", "eslint-config-next": "15.0.4", "eslint-config-prettier": "9.1.0", "eslint-plugin-import": "^2.26.0", "eslint-plugin-jsx-a11y": "^6.4.1", "eslint-plugin-node": "^11.1.0", "eslint-plugin-prettier": "5.2.1", "eslint-plugin-react": "^7.23.2", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-unused-imports": "4.1.4", "postcss": "8.4.49", "prettier": "^3.5.3", "prettier-plugin-tailwindcss": "^0.6.11", "tailwind-variants": "0.3.0", "tailwindcss": "3.4.16", "tsx": "^4.20.3", "typescript": "5.6.3"}}