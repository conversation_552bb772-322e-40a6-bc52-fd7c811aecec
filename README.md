# Church Scheduler

A modern church scheduling application built with Next.js 15, Hero<PERSON> (v2), and PostgreSQL. Manage worship team schedules, events, and member availability with ease.

## Features

- 📅 **Event Management**: Create and manage church events with full calendar integration
- 👥 **Team Management**: Track team members, roles, and availability
- 🚫 **Blockout Dates**: Allow team members to set unavailable dates
- 📱 **Responsive Design**: Works seamlessly on desktop and mobile devices
- 🎨 **Modern UI**: Beautiful interface built with HeroUI components
- 🗄️ **Database Integration**: PostgreSQL database with automatic migrations

## Technologies Used

- [Next.js 15](https://nextjs.org/docs/getting-started) - React framework with App Router
- [HeroUI v2](https://heroui.com/) - Modern React UI library
- [PostgreSQL](https://www.postgresql.org/) - Robust relational database
- [Drizzle ORM](https://orm.drizzle.team/) - TypeScript ORM with automatic migrations
- [Vercel Postgres](https://vercel.com/storage/postgres) - Managed PostgreSQL database
- [FullCalendar](https://fullcalendar.io/) - Feature-rich calendar component
- [Tailwind CSS](https://tailwindcss.com/) - Utility-first CSS framework
- [TypeScript](https://www.typescriptlang.org/) - Type-safe JavaScript
- [Framer Motion](https://www.framer.com/motion/) - Animation library
- [SWR](https://swr.vercel.app/) - Data fetching library

## Quick Start

### Prerequisites

- Node.js 18+ installed
- Yarn package manager (recommended)

### Local Development

1. **Clone the repository**:

   ```bash
   git clone <your-repo-url>
   cd church-scheduler
   ```

2. **Install dependencies**:

   ```bash
   yarn install
   ```

3. **Set up environment variables**:

   ```bash
   cp .env.example .env.local
   # Edit .env.local with your database configuration
   ```

4. **Set up the database**:

   ```bash
   # Generate migration files
   yarn db:generate

   # Run migrations
   yarn db:migrate

   # Seed with initial data
   yarn db:seed
   ```

5. **Start the development server**:

   ```bash
   yarn dev
   ```

6. **Open your browser** and navigate to [http://localhost:3000](http://localhost:3000)

## Database Commands

```bash
# Generate new migration after schema changes
yarn db:generate

# Run migrations
yarn db:migrate

# Seed database with initial data
yarn db:seed

# Open Drizzle Studio (database GUI)
yarn db:studio

# Push schema changes directly (development only)
yarn db:push
```

## Deployment

This application is optimized for deployment on Vercel with automatic database migrations.

### Deploy to Vercel

1. **Set up Vercel Postgres**:

   - Create a new Postgres database in your Vercel dashboard
   - Connect it to your project

2. **Deploy**:
   - Push your code to GitHub/GitLab/Bitbucket
   - Import the project in Vercel
   - Vercel will automatically run migrations and deploy

For detailed deployment instructions, see [DEPLOYMENT.md](./DEPLOYMENT.md).

## License

Licensed under the [MIT license](https://github.com/heroui-inc/next-app-template/blob/main/LICENSE).
