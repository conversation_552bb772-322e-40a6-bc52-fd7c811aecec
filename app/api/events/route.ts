import { NextResponse } from "next/server";
import { events } from "@/app/data/events";
import { CalendarEvent } from "@/types/events";

export async function GET() {
  // Simulate a delay like a real API would have
  await new Promise((resolve) => setTimeout(resolve, 500));

  // Convert date strings back to Date objects and add IDs if missing
  const formattedEvents = events.map((event, index) => ({
    ...event,
    id: event.id || `event-${index}`,
    start: event.start,
    end: event.end,
    allDay: event.allDay || false,
  }));

  return NextResponse.json({ events: formattedEvents });
}

export async function POST(request: Request) {
  const body = await request.json();

  // Generate a unique ID for the new event
  const newEvent: CalendarEvent = {
    id: `event-${events.length}`,
    ...body,
  };
  events.push(newEvent);
  // In a real app, we would save this to a database
  // For now, we'll just return the new event
  return NextResponse.json({ event: newEvent });
}
