import { NextResponse } from "next/server";
import { users } from "@/app/data/users";
import { TeamMember } from "@/types/user";

/**
 *
 * @returns Returns a list of users
 */
export async function GET() {
  // Simulate a delay like a real API would have
  await new Promise((resolve) => setTimeout(resolve, 500));

  return NextResponse.json({ users });
}

export async function POST(request: Request) {
  const body = await request.json();
  const newUser: TeamMember = {
    ...body,
    key: users.length > 0 ? Math.max(...users.map((user) => user.key)) + 1 : 0,
  };

  // In a real app, we would save this to a database
  // For now, we'll just return the new user
  return NextResponse.json({ user: newUser });
}
