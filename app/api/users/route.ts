import { NextResponse } from "next/server";
import { db } from "@/lib/db/config";
import { users } from "@/lib/db/schema";
import { TeamMember } from "@/types/user";
import { sql } from "drizzle-orm";

/**
 *
 * @returns Returns a list of users
 */
export async function GET() {
  try {
    // Fetch users from database
    const dbUsers = await db.select().from(users);

    // Convert to the format expected by the frontend
    const formattedUsers: TeamMember[] = dbUsers.map((user) => ({
      key: user.key,
      name: user.name,
      role: user.role,
      status: user.status as "available" | "unavailable" | "tentative",
      blockout: "11/04 - 12/31", // TODO: Calculate from blockouts table
      email: user.email || undefined,
      phone: user.phone || undefined,
      avatar: user.avatar || undefined,
    }));

    return NextResponse.json({ users: formattedUsers });
  } catch (error) {
    console.error("Error fetching users:", error);
    return NextResponse.json(
      { error: "Failed to fetch users" },
      { status: 500 },
    );
  }
}

export async function POST(request: Request) {
  try {
    const body = await request.json();

    // Get the next available key
    const maxKeyResult = await db
      .select({ maxKey: sql<number>`COALESCE(MAX(${users.key}), -1)` })
      .from(users);
    const nextKey = (maxKeyResult[0]?.maxKey ?? -1) + 1;

    // Insert new user into database
    const [newUser] = await db
      .insert(users)
      .values({
        key: nextKey,
        name: body.name,
        role: body.role,
        status: body.status || "available",
        email: body.email,
        phone: body.phone,
        avatar: body.avatar,
      })
      .returning();

    // Convert to the format expected by the frontend
    const formattedUser: TeamMember = {
      key: newUser.key,
      name: newUser.name,
      role: newUser.role,
      status: newUser.status as "available" | "unavailable" | "tentative",
      blockout: "11/04 - 12/31", // TODO: Calculate from blockouts table
      email: newUser.email || undefined,
      phone: newUser.phone || undefined,
      avatar: newUser.avatar || undefined,
    };

    return NextResponse.json({ user: formattedUser });
  } catch (error) {
    console.error("Error creating user:", error);
    return NextResponse.json(
      { error: "Failed to create user" },
      { status: 500 },
    );
  }
}
