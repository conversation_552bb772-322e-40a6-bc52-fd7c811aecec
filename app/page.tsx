"use client";

import MainCalendar from "@/components/main-calendar";
import UsersTable from "@/components/users-table";
import { useEvents } from "@/hooks/useEvents";
import { useUsers } from "@/hooks/useUsers";
import { Card, CardBody } from "@heroui/card";

export default function Home() {
  const { isError: eventsError } = useEvents();
  const { users, isLoading: usersLoading, isError: usersError } = useUsers();

  return (
    <>
      <section>
        <h1 className="mb-5 text-3xl font-semibold">Hi, User!</h1>

        <h1 className="text-2xl font-semibold">Master Calendar</h1>
        <p className="text-default-500">
          Schedule and manage all your events in one place
        </p>
        <Card className="my-5">
          <CardBody>
            <MainCalendar />
          </CardBody>
        </Card>
        {eventsError && (
          <div className="mt-4 text-center text-danger">
            Error loading events. Please try again later.
          </div>
        )}

        <h1 className="text-2xl font-semibold">Current Blockouts</h1>
        <p className="text-default-500">
          View and manage your team members blockouts
        </p>

        <div className="my-5">
          <UsersTable teamMembers={users} isLoading={usersLoading} />
          {usersError && (
            <div className="mt-4 text-center text-danger">
              Error loading users. Please try again later.
            </div>
          )}
        </div>
      </section>
    </>
  );
}
