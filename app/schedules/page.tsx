"use client";

import {
  <PERSON><PERSON>,
  DateRange<PERSON>icker,
  get<PERSON>ey<PERSON><PERSON><PERSON>,
  <PERSON>dal,
  ModalBody,
  Modal<PERSON>ontent,
  <PERSON>dal<PERSON>ooter,
  ModalHeader,
  Pagination,
  Table,
  TableBody,
  TableCell,
  TableColumn,
  TableHeader,
  TableRow,
  useDisclosure,
} from "@heroui/react";
import { useMemo, useState } from "react";

interface BlockOutDate {
  timeoff: string;
  userId: string;
}

const columns = [{ uid: "timeoff", date: "Time Off" }];

export default function MySchedulePage() {
  const [page, setPage] = useState(1);
  const rowsPerPage = 4;
  const pages = Math.ceil([].length / rowsPerPage);
  const [timeOffDates, setTimeOffDates] = useState<BlockOutDate[]>();
  const { isOpen, onOpen, onClose } = useDisclosure();

  const handleOpen = () => {
    onOpen();
  };

  const items = useMemo(() => {
    if (timeOffDates) {
      const start = (page - 1) * rowsPerPage;
      const end = start + rowsPerPage;

      return timeOffDates.slice(start, end);
    }
    return [];
  }, [page, timeOffDates]);

  return (
    <div>
      <h1 className="mb-5 text-center text-3xl font-semibold lg:text-left">
        My Schedules
      </h1>

      <h2 className="text-xl font-medium">List of Blockout</h2>
      <p className="my-5">Table View</p>
      <Table
        aria-label="Calendar Scheduler"
        isStriped
        isCompact
        radius="none"
        bottomContent={
          <div className="flex items-center justify-between px-2 py-2">
            <Pagination
              classNames={{
                cursor: "bg-foreground text-background",
              }}
              isCompact
              showControls
              showShadow
              color="default"
              page={page}
              total={pages}
              variant="light"
              onChange={(page) => setPage(page)}
            />
          </div>
        }
      >
        <TableHeader columns={columns}>
          {(column) => (
            <TableColumn key={column.uid}>{column.date}</TableColumn>
          )}
        </TableHeader>
        <TableBody items={items}>
          {(blockout) => (
            <TableRow key={blockout.userId}>
              {(columnKey) => (
                <TableCell>{getKeyValue(blockout, columnKey)}</TableCell>
              )}
            </TableRow>
          )}
        </TableBody>
      </Table>

      <div className="my-16">
        <Button onPress={handleOpen}>Create Blockout</Button>

        <Modal isOpen={isOpen} size="xl" onClose={onClose}>
          <ModalContent>
            {(onClose) => (
              <>
                <ModalHeader className="flex flex-col gap-1">
                  Select your blockout dates
                </ModalHeader>

                <ModalBody>
                  <p>
                    Select the dates you are not going to available to service
                    with the chior.
                  </p>
                  <DateRangePicker
                    onChange={(e) => {
                      const dates: BlockOutDate[] = [];
                      dates.push({
                        userId: "jo-1",
                        timeoff: `${e?.start} - ${e?.end}`,
                      });
                      if (timeOffDates) {
                        setTimeOffDates([...dates, ...timeOffDates]);
                      } else setTimeOffDates(dates);
                    }}
                    // isInvalid
                    className="max-w-xs"
                    // errorMessage="Please enter your stay duration"
                    label="Time off duration"
                    variant="bordered"
                  />
                </ModalBody>
                <ModalFooter>
                  <Button color="danger" variant="light" onPress={onClose}>
                    Close
                  </Button>
                  <Button color="default" onPress={onClose}>
                    Submit Dates
                  </Button>
                </ModalFooter>
              </>
            )}
          </ModalContent>
        </Modal>
      </div>
    </div>
  );
}
