{"id": "9e83be6a-b0f4-44d4-a907-1dd156562a55", "prevId": "00000000-0000-0000-0000-000000000000", "version": "7", "dialect": "postgresql", "tables": {"public.blockouts": {"name": "blockouts", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "user_id": {"name": "user_id", "type": "integer", "primaryKey": false, "notNull": true}, "user_key": {"name": "user_key", "type": "integer", "primaryKey": false, "notNull": true}, "start_date": {"name": "start_date", "type": "timestamp", "primaryKey": false, "notNull": true}, "end_date": {"name": "end_date", "type": "timestamp", "primaryKey": false, "notNull": true}, "reason": {"name": "reason", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"blockouts_user_id_users_id_fk": {"name": "blockouts_user_id_users_id_fk", "tableFrom": "blockouts", "tableTo": "users", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "blockouts_user_key_users_key_fk": {"name": "blockouts_user_key_users_key_fk", "tableFrom": "blockouts", "tableTo": "users", "columnsFrom": ["user_key"], "columnsTo": ["key"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.events": {"name": "events", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "title": {"name": "title", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": false}, "location": {"name": "location", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "start": {"name": "start", "type": "timestamp", "primaryKey": false, "notNull": true}, "end": {"name": "end", "type": "timestamp", "primaryKey": false, "notNull": true}, "all_day": {"name": "all_day", "type": "boolean", "primaryKey": false, "notNull": false, "default": false}, "color": {"name": "color", "type": "<PERSON><PERSON><PERSON>(7)", "primaryKey": false, "notNull": false}, "team_member_key": {"name": "team_member_key", "type": "integer", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"events_team_member_key_users_key_fk": {"name": "events_team_member_key_users_key_fk", "tableFrom": "events", "tableTo": "users", "columnsFrom": ["team_member_key"], "columnsTo": ["key"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.users": {"name": "users", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "key": {"name": "key", "type": "integer", "primaryKey": false, "notNull": true}, "name": {"name": "name", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "role": {"name": "role", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "status": {"name": "status", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": true, "default": "'available'"}, "email": {"name": "email", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "phone": {"name": "phone", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": false}, "avatar": {"name": "avatar", "type": "var<PERSON><PERSON>(500)", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {"users_key_unique": {"name": "users_key_unique", "nullsNotDistinct": false, "columns": ["key"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}}, "enums": {}, "schemas": {}, "sequences": {}, "roles": {}, "policies": {}, "views": {}, "_meta": {"columns": {}, "schemas": {}, "tables": {}}}