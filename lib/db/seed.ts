import { db } from './config';
import { users, events, blockouts } from './schema';
import { eq } from 'drizzle-orm';

const sampleUsers = [
  {
    key: 0,
    name: "<PERSON>",
    role: "Lead Vocal",
    status: "available" as const,
    email: "<EMAIL>",
  },
  {
    key: 1,
    name: "<PERSON>",
    role: "<PERSON>",
    status: "tentative" as const,
    email: "<EMAIL>",
  },
  {
    key: 2,
    name: "<PERSON>",
    role: "<PERSON>",
    status: "available" as const,
    email: "<EMAIL>",
  },
  {
    key: 3,
    name: "<PERSON>",
    role: "<PERSON>",
    status: "unavailable" as const,
    email: "<EMAIL>",
  },
  {
    key: 4,
    name: "Eve",
    role: "Backup Vocal",
    status: "available" as const,
    email: "<EMAIL>",
  },
];

const sampleEvents = [
  {
    title: "Sunday Service",
    description: "Regular Sunday worship service",
    start: new Date(2025, 3, 27, 10, 0), // April 27, 2025, 10:00 AM
    end: new Date(2025, 3, 27, 11, 30),
    allDay: false,
  },
  {
    title: "Midweek Rehearsal",
    description: "Practice session for the worship team",
    start: new Date(2025, 4, 1, 19, 0), // May 1, 2025, 7:00 PM
    end: new Date(2025, 4, 1, 21, 0),
    allDay: false,
  },
  {
    title: "Sunday Service",
    description: "Regular Sunday worship service",
    start: new Date(2025, 4, 25, 10, 0), // May 25, 2025, 10:00 AM
    end: new Date(2025, 4, 25, 11, 30),
    allDay: false,
  },
];

export async function seedDatabase() {
  console.log('🌱 Seeding database...');
  
  try {
    // Check if users already exist
    const existingUsers = await db.select().from(users).limit(1);
    
    if (existingUsers.length === 0) {
      console.log('📝 Inserting sample users...');
      await db.insert(users).values(sampleUsers);
    } else {
      console.log('👥 Users already exist, skipping user seeding');
    }

    // Check if events already exist
    const existingEvents = await db.select().from(events).limit(1);
    
    if (existingEvents.length === 0) {
      console.log('📅 Inserting sample events...');
      await db.insert(events).values(sampleEvents);
    } else {
      console.log('📅 Events already exist, skipping event seeding');
    }

    console.log('✅ Database seeded successfully');
  } catch (error) {
    console.error('❌ Seeding failed:', error);
    throw error;
  }
}

// Run seeding if this file is executed directly
if (require.main === module) {
  seedDatabase()
    .then(() => {
      console.log('Seeding process completed');
      process.exit(0);
    })
    .catch((error) => {
      console.error('Seeding process failed:', error);
      process.exit(1);
    });
}
