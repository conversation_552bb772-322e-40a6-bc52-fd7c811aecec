"use client";

import { TeamMember, UserStatus } from "@/types/user";
import {
  Chip,
  ChipProps,
  Pagination,
  Table,
  TableBody,
  TableCell,
  TableColumn,
  TableHeader,
  TableRow,
  Dropdown,
  DropdownTrigger,
  DropdownMenu,
  DropdownItem,
  Button,
  Input,
  Spinner,
  Avatar,
} from "@heroui/react";
import React, { Key, useCallback, useMemo, useState } from "react";
import { SearchIcon } from "@/components/icons";

const statusColorMap: Record<UserStatus, ChipProps["color"]> = {
  available: "success",
  unavailable: "danger",
  tentative: "warning",
};

const columns = [
  { name: "Team Member", uid: "name" },
  { name: "Role", uid: "role" },
  { name: "Status", uid: "status" },
  { name: "Date Blockout", uid: "dateBlockout" },
  { name: "Actions", uid: "actions" },
];

interface UsersTableProps {
  teamMembers: TeamMember[];
  isLoading?: boolean;
}

const UsersTable = ({ teamMembers, isLoading = false }: UsersTableProps) => {
  const [page, setPage] = useState(1);
  const [filterValue, setFilterValue] = useState("");
  const [statusFilter, setStatusFilter] = useState<UserStatus | "all">("all");
  const rowsPerPage = 4;

  const filteredItems = useMemo(() => {
    let filteredMembers = [...teamMembers];

    if (filterValue) {
      filteredMembers = filteredMembers.filter(
        (member) =>
          member.name.toLowerCase().includes(filterValue.toLowerCase()) ||
          member.role.toLowerCase().includes(filterValue.toLowerCase()),
      );
    }

    if (statusFilter !== "all") {
      filteredMembers = filteredMembers.filter(
        (member) => member.status === statusFilter,
      );
    }

    return filteredMembers;
  }, [teamMembers, filterValue, statusFilter]);

  const pages = Math.ceil(filteredItems.length / rowsPerPage);

  const renderCell = useCallback((teamMember: TeamMember, columnKey: Key) => {
    const cellValue = teamMember[columnKey as keyof TeamMember];

    switch (columnKey) {
      case "name":
        return (
          <div className="flex items-center gap-3">
            <Avatar
              name={teamMember.name}
              size="sm"
              src={teamMember.avatar || undefined}
            />
            <p className="text-bold text-sm capitalize">{teamMember.name}</p>
          </div>
        );
      case "role":
        return (
          <div className="flex flex-col">
            <p className="text-bold text-sm capitalize">{teamMember.role}</p>
          </div>
        );
      case "status":
        return (
          <Chip
            className="capitalize"
            color={statusColorMap[teamMember.status as UserStatus]}
            size="sm"
            variant="flat"
          >
            {cellValue}
          </Chip>
        );
      case "dateBlockout":
        return (
          <Chip size="sm" variant="flat">
            {teamMember.blockout}
          </Chip>
        );
      case "actions":
        return (
          <div className="flex justify-end gap-2">
            <Dropdown>
              <DropdownTrigger>
                <Button isIconOnly size="sm" variant="light">
                  •••
                </Button>
              </DropdownTrigger>
              <DropdownMenu aria-label="Actions">
                <DropdownItem key="view">View Details</DropdownItem>
                <DropdownItem key="edit">Edit</DropdownItem>
                <DropdownItem
                  key="delete"
                  className="text-danger"
                  color="danger"
                >
                  Delete
                </DropdownItem>
              </DropdownMenu>
            </Dropdown>
          </div>
        );
      default:
        return cellValue;
    }
  }, []);

  const items = useMemo(() => {
    const start = (page - 1) * rowsPerPage;
    const end = start + rowsPerPage;

    return filteredItems.slice(start, end);
  }, [page, filteredItems]);

  const topContent = useMemo(() => {
    return (
      <div className="flex flex-col gap-4">
        <div className="flex items-center justify-between gap-3">
          <Input
            isClearable
            className="w-full sm:max-w-[44%]"
            placeholder="Search by name or role..."
            startContent={<SearchIcon />}
            value={filterValue}
            onClear={() => setFilterValue("")}
            onValueChange={setFilterValue}
          />
          <div className="flex gap-3">
            <Dropdown>
              <DropdownTrigger className="hidden sm:flex">
                <Button
                  endContent={<span className="text-small">▼</span>}
                  variant="flat"
                >
                  Status
                </Button>
              </DropdownTrigger>
              <DropdownMenu
                disallowEmptySelection
                aria-label="Table Columns"
                closeOnSelect={false}
                selectedKeys={statusFilter}
                selectionMode="single"
                onSelectionChange={(keys) => {
                  const selected = Array.from(keys)[0] as UserStatus | "all";
                  setStatusFilter(selected);
                }}
              >
                <DropdownItem key="all">All</DropdownItem>
                <DropdownItem key="available">Available</DropdownItem>
                <DropdownItem key="tentative">Tentative</DropdownItem>
                <DropdownItem key="unavailable">Unavailable</DropdownItem>
              </DropdownMenu>
            </Dropdown>
          </div>
        </div>
        <div className="flex items-center justify-between">
          <span className="text-small text-default-400">
            Total {filteredItems.length} team members
          </span>
        </div>
      </div>
    );
  }, [filterValue, statusFilter, filteredItems.length]);

  if (isLoading) {
    return (
      <div className="flex h-[300px] items-center justify-center">
        <Spinner size="lg" />
      </div>
    );
  }

  return (
    <Table
      aria-label="Calendar Scheduler"
      isStriped
      isCompact
      radius="none"
      topContent={topContent}
      bottomContent={
        pages > 0 ? (
          <div className="flex items-center justify-between px-2 py-2">
            <span className="text-small text-default-400">
              Showing {items.length} of {filteredItems.length} entries
            </span>
            <Pagination
              classNames={{
                cursor: "bg-foreground text-background",
              }}
              isCompact
              showControls
              showShadow
              color="default"
              page={page}
              total={pages}
              variant="light"
              onChange={(page) => setPage(page)}
            />
          </div>
        ) : null
      }
    >
      <TableHeader columns={columns}>
        {(column) => (
          <TableColumn
            key={column.uid}
            align={column.uid === "actions" ? "center" : "start"}
          >
            {column.name}
          </TableColumn>
        )}
      </TableHeader>
      <TableBody items={items} emptyContent={"No team members found"}>
        {(teamMember) => (
          <TableRow key={teamMember.key}>
            {(columnKey) => (
              <TableCell>{renderCell(teamMember, columnKey)}</TableCell>
            )}
          </TableRow>
        )}
      </TableBody>
    </Table>
  );
};

export default UsersTable;
