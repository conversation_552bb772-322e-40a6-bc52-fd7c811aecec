"use client";

import React, { useState } from "react";
import FullCalendar from "@fullcalendar/react";
import dayGridPlugin from "@fullcalendar/daygrid";
import interactionPlugin from "@fullcalendar/interaction";
import { CalendarEvent } from "@/types/events";
import { Spinner } from "@heroui/spinner";
import ViewEventModal from "./modals/view-event-modal";
import AddEventModal from "./modals/add-event-modal";
import { useEvents } from "@/hooks/useEvents";

const CalendarView = () => {
  const { events, isLoading, isError: eventsError } = useEvents();
  const [selectedEvent, setSelectedEvent] = useState<CalendarEvent | null>(
    null,
  );
  const [isViewEventModalOpen, setIsViewEventModalOpen] = useState(false);
  const [isAddEventModalOpen, setIsAddEventModalOpen] = useState(false);

  // Format events for FullCalendar
  const formattedEvents = events.map((event) => ({
    id: event.id?.toString() || Math.random().toString(),
    title: event.title,
    start: event.start,
    end: event.end,
    allDay: event.allDay,
    extendedProps: {
      description: event.description,
      teamMemberKey: event.teamMemberKey,
    },
  }));

  const handleEventViewClick = (info: any) => {
    // Find the event in the original events array
    const event = events.find((event) => {
      return event.id?.toString() === info.event.id;
    });

    if (event) {
      setSelectedEvent(event);
      setIsViewEventModalOpen(true);
    }
  };

  const handleAddNewEventClick = (selectInfo: any) => {
    setSelectedEvent(selectInfo);
    setIsAddEventModalOpen(true);
  };

  if (isLoading) {
    return (
      <div className="flex h-[500px] items-center justify-center">
        <Spinner size="lg" />
      </div>
    );
  }

  return (
    <>
      <div className="calendar-container">
        <FullCalendar
          plugins={[
            dayGridPlugin,
            // timeGridPlugin,
            interactionPlugin,
            // listPlugin,
          ]}
          initialView={
            window.innerWidth < 768 ? "dayGridMonth" : "dayGridMonth"
          }
          headerToolbar={{
            left: "prev,next",
            center: "title",
            // right: "dayGridMonth,timeGridWeek,timeGridDay,listWeek",
            right: "today",
          }}
          events={formattedEvents}
          selectable={true}
          select={handleAddNewEventClick}
          eventClick={handleEventViewClick}
          height={600}
          editable={true}
          dayMaxEvents={true}
          nowIndicator={true}
          eventTimeFormat={{
            hour: "numeric",
            minute: "2-digit",
            meridiem: "short",
          }}
          windowResize={(arg) => {
            if (window.innerWidth < 768) {
              arg.view.calendar.changeView("dayGridMonth");
            } else {
              arg.view.calendar.changeView("dayGridMonth");
            }
          }}
          views={{
            dayGridMonth: {
              titleFormat: { year: "numeric", month: "long" },
            },
            timeGridWeek: {
              titleFormat: { year: "numeric", month: "short", day: "numeric" },
            },
            timeGridDay: {
              titleFormat: { year: "numeric", month: "long", day: "numeric" },
            },
            listWeek: {
              titleFormat: { year: "numeric", month: "long" },
            },
          }}
        />
      </div>

      <ViewEventModal
        isOpen={isViewEventModalOpen}
        onClose={() => setIsViewEventModalOpen(false)}
        selectedEvent={selectedEvent}
      />

      <AddEventModal
        isOpen={isAddEventModalOpen}
        onClose={() => {
          setIsAddEventModalOpen(false);
        }}
        selectedEvent={selectedEvent}
      />
    </>
  );
};

export default CalendarView;
