import useSWR from "swr";
import { CalendarEvent } from "@/types/events";
import useSWRMutation from "swr/mutation";

const getEvents = (url: string) => fetch(url).then((res) => res.json());
const addEvent = async (url: string, { arg }: { arg: CalendarEvent }) => {
  const res = await fetch(url, {
    method: "POST",
    headers: {
      "Content-Type": "application/json",
    },
    body: JSON.stringify(arg),
  });

  if (!res.ok) {
    throw new Error("Failed to add event");
  }

  return res.json();
};

export function useEvents() {
  const { data, error, isLoading } = useSWR("/api/events", getEvents);
  const { trigger, isMutating } = useSWRMutation("/api/events", addEvent);

  return {
    events: (data?.events as CalendarEvent[]) || [],
    addNewEvent: trigger,
    isAddingNewEvent: isMutating,
    isLoading,
    isError: error,
  };
}
